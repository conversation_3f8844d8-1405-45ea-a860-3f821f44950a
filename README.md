# 剪映文本特效预览生成器

这是一个用于读取剪映工程文件中的文本特效，并生成自定义文字预览APNG动图的工具。

## 功能特点

- 🎬 读取剪映工程文件（draft_info.json）
- 📝 提取文本特效和样式信息
- 🎨 支持字体、颜色、阴影、描边等特效渲染
- 🎞️ 支持动画效果（淡入、淡出、循环等）
- 🖼️ 生成高质量APNG动图预览
- 🖥️ 用户友好的图形界面
- ⚡ 实时预览和播放控制

## 系统要求

- Python 3.7+
- macOS / Windows / Linux

## 安装依赖

```bash
pip install pillow
```

注：tkinter通常随Python安装，无需额外安装。

## 使用方法

### 1. 启动程序

```bash
python main.py
```

或直接双击 `main.py` 文件（如果已配置Python关联）。

### 2. 加载剪映工程

1. 点击"浏览"按钮选择剪映工程文件夹
2. 点击"加载工程"按钮解析工程文件
3. 程序会自动提取所有文本特效

### 3. 选择特效和输入文字

1. 在左侧"文本特效"列表中选择要使用的特效
2. 在"自定义文本"输入框中输入要预览的文字
3. 查看右侧的特效信息

### 4. 生成预览

1. 点击"生成预览"按钮
2. 等待渲染完成
3. 在预览区域查看效果

### 5. 播放控制

- 点击"播放"按钮播放动画
- 点击预览区域手动切换帧（暂停状态下）

### 6. 导出APNG

1. 点击"导出APNG"按钮
2. 选择保存位置和文件名
3. 等待导出完成

## 文件结构

```
剪映文本特效预览生成器/
├── main.py                 # 主程序入口
├── text_effect_gui.py      # 用户界面
├── jianying_parser.py      # 剪映工程解析器
├── text_renderer.py        # 文本特效渲染引擎
├── apng_generator.py       # APNG动图生成器
└── README.md              # 说明文档
```

## 支持的特效

### 文本样式
- ✅ 字体和字号
- ✅ 填充颜色
- ✅ 描边效果
- ✅ 阴影效果
- ✅ 内阴影
- ✅ 斜体

### 动画效果
- ✅ 淡入动画
- ✅ 淡出动画
- ✅ 循环动画
- ✅ 自定义时长

## 技术实现

### 核心组件

1. **JianyingProjectParser**: 解析剪映工程文件，提取文本特效信息
2. **TextRenderer**: 使用PIL渲染文本特效，支持各种样式和动画
3. **APNGGenerator**: 生成APNG动图文件
4. **TextEffectGUI**: 提供用户友好的图形界面

### 渲染流程

1. 解析剪映工程文件中的文本特效配置
2. 提取字体、颜色、阴影等样式信息
3. 根据动画配置生成帧序列
4. 应用特效渲染每一帧
5. 合成APNG动图

## 注意事项

1. **字体支持**: 程序会尝试使用剪映工程中指定的字体，如果字体文件不存在，会使用系统默认字体
2. **性能**: 渲染高分辨率动画可能需要较长时间，请耐心等待
3. **文件格式**: 主要支持APNG格式，如果保存失败会自动尝试GIF格式
4. **工程兼容性**: 支持剪映5.x版本的工程文件格式

## 故障排除

### 常见问题

**Q: 程序启动失败**
A: 请检查Python版本（需要3.7+）和依赖库是否正确安装

**Q: 加载工程失败**
A: 请确保选择的是剪映工程文件夹，且包含draft_info.json文件

**Q: 预览生成失败**
A: 可能是字体文件缺失或特效配置异常，请查看错误信息

**Q: 导出APNG失败**
A: 程序会自动尝试导出为GIF格式作为备选方案

### 调试信息

程序运行时会在控制台输出详细的调试信息，遇到问题时请查看控制台输出。

## 开发说明

### 扩展功能

如需添加新的特效支持，可以：

1. 在 `TextRenderer` 类中添加新的渲染方法
2. 在 `JianyingProjectParser` 中添加新的解析逻辑
3. 更新GUI界面以支持新功能

### 贡献代码

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本文本特效解析和渲染
- 支持APNG动图生成
- 提供图形用户界面

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映工程文件解析器
用于解析剪映工程文件中的文本特效、字体、动画等信息
"""

import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path


@dataclass
class TextStyle:
    """文本样式信息"""
    font_path: str = ""
    font_id: str = ""
    size: float = 15.0
    italic: bool = False
    fill_color: List[float] = None
    stroke_color: List[float] = None
    stroke_width: float = 0.0
    shadows: List[Dict] = None
    inner_shadows: List[Dict] = None
    strokes: List[Dict] = None
    effect_style_path: str = ""
    effect_style_id: str = ""
    
    def __post_init__(self):
        if self.fill_color is None:
            self.fill_color = [1.0, 1.0, 1.0, 1.0]
        if self.shadows is None:
            self.shadows = []
        if self.inner_shadows is None:
            self.inner_shadows = []
        if self.strokes is None:
            self.strokes = []


@dataclass
class TextAnimation:
    """文本动画信息"""
    animation_id: str = ""
    duration: int = 0
    start_time: int = 0
    animation_type: str = ""  # in, out, loop
    path: str = ""
    resource_id: str = ""


@dataclass
class TextEffect:
    """文本特效信息"""
    id: str = ""
    name: str = ""
    text_content: str = ""
    style: TextStyle = None
    animations: List[TextAnimation] = None
    transform: Dict = None
    duration: int = 3000000
    
    def __post_init__(self):
        if self.style is None:
            self.style = TextStyle()
        if self.animations is None:
            self.animations = []
        if self.transform is None:
            self.transform = {
                "x": 0.0, "y": 0.0, "rotation": 0.0,
                "scale_x": 1.0, "scale_y": 1.0, "alpha": 1.0
            }


class JianyingProjectParser:
    """剪映工程文件解析器"""
    
    def __init__(self, project_path: str):
        """
        初始化解析器
        
        Args:
            project_path: 剪映工程文件夹路径
        """
        self.project_path = Path(project_path)
        self.draft_info_path = self.project_path / "draft_info.json"
        self.project_data = None
        self.text_effects = []
        
        if not self.draft_info_path.exists():
            raise FileNotFoundError(f"找不到剪映工程文件: {self.draft_info_path}")
    
    def load_project(self) -> Dict:
        """加载剪映工程文件"""
        try:
            with open(self.draft_info_path, 'r', encoding='utf-8') as f:
                self.project_data = json.load(f)
            return self.project_data
        except Exception as e:
            raise Exception(f"加载剪映工程文件失败: {e}")
    
    def parse_text_style(self, content_str: str) -> TextStyle:
        """解析文本样式信息"""
        try:
            content = json.loads(content_str)
            styles = content.get('styles', [])
            
            if not styles:
                return TextStyle()
            
            style_data = styles[0]  # 取第一个样式
            text_style = TextStyle()
            
            # 字体信息
            font_info = style_data.get('font', {})
            text_style.font_path = font_info.get('path', '')
            text_style.font_id = font_info.get('id', '')
            
            # 基本样式
            text_style.size = style_data.get('size', 15.0)
            text_style.italic = style_data.get('italic', False)
            
            # 填充颜色
            fill_info = style_data.get('fill', {})
            if fill_info:
                solid_color = fill_info.get('content', {}).get('solid', {}).get('color', [1, 1, 1])
                text_style.fill_color = solid_color + [1.0] if len(solid_color) == 3 else solid_color
            
            # 描边信息
            strokes = style_data.get('strokes', [])
            if strokes:
                stroke = strokes[0]
                text_style.stroke_width = stroke.get('width', 0.0)
                stroke_color = stroke.get('content', {}).get('solid', {}).get('color', [0, 0, 0])
                text_style.stroke_color = stroke_color + [1.0] if len(stroke_color) == 3 else stroke_color
            
            # 阴影信息
            text_style.shadows = style_data.get('shadows', [])
            text_style.inner_shadows = style_data.get('inner_shadows', [])
            text_style.strokes = strokes
            
            # 特效样式
            effect_style = style_data.get('effectStyle', {})
            text_style.effect_style_path = effect_style.get('path', '')
            text_style.effect_style_id = effect_style.get('id', '')
            
            return text_style
            
        except Exception as e:
            print(f"解析文本样式失败: {e}")
            return TextStyle()
    
    def parse_animations(self, material_animations: List[Dict], effect_refs: List[str]) -> List[TextAnimation]:
        """解析动画信息"""
        animations = []
        
        for material_anim in material_animations:
            if material_anim.get('id') in effect_refs:
                for anim in material_anim.get('animations', []):
                    text_anim = TextAnimation(
                        animation_id=anim.get('id', ''),
                        duration=anim.get('duration', 0),
                        start_time=anim.get('start', 0),
                        animation_type=anim.get('type', ''),
                        path=anim.get('path', ''),
                        resource_id=anim.get('resource_id', '')
                    )
                    animations.append(text_anim)
        
        return animations
    
    def extract_text_effects(self) -> List[TextEffect]:
        """提取所有文本特效"""
        if not self.project_data:
            self.load_project()
        
        self.text_effects = []
        materials = self.project_data.get('materials', {})
        
        # 解析文本模板
        text_templates = materials.get('text_templates', [])
        material_animations = materials.get('material_animations', [])
        
        for template in text_templates:
            text_effect = TextEffect()
            text_effect.id = template.get('id', '')
            text_effect.name = template.get('name', '')
            
            # 获取文本信息资源
            text_info_resources = template.get('text_info_resources', [])
            if text_info_resources:
                text_info = text_info_resources[0]
                attach_info = text_info.get('attach_info', {})
                text_effect.duration = attach_info.get('duration', 3000000)
                
                # 变换信息
                clip = attach_info.get('clip', {})
                text_effect.transform = {
                    'x': clip.get('transform', {}).get('x', 0.0),
                    'y': clip.get('transform', {}).get('y', 0.0),
                    'rotation': clip.get('rotation', 0.0),
                    'scale_x': clip.get('scale', {}).get('x', 1.0),
                    'scale_y': clip.get('scale', {}).get('y', 1.0),
                    'alpha': clip.get('alpha', 1.0)
                }
                
                # 获取动画信息
                effect_refs = text_info.get('extra_material_refs', [])
                text_effect.animations = self.parse_animations(material_animations, effect_refs)
        
        # 解析文本对象
        texts = materials.get('texts', [])
        for text_obj in texts:
            # 如果已经在模板中处理过，跳过
            text_id = text_obj.get('id', '')
            if any(effect.id == text_id for effect in self.text_effects):
                continue
            
            text_effect = TextEffect()
            text_effect.id = text_id
            text_effect.name = text_obj.get('name', '')
            
            # 解析文本内容和样式
            content = text_obj.get('content', '')
            if content:
                try:
                    content_data = json.loads(content)
                    text_effect.text_content = content_data.get('text', '')
                    text_effect.style = self.parse_text_style(content)
                except:
                    text_effect.text_content = ''
                    text_effect.style = TextStyle()
            
            self.text_effects.append(text_effect)
        
        return self.text_effects
    
    def get_canvas_config(self) -> Dict:
        """获取画布配置"""
        if not self.project_data:
            self.load_project()
        
        return self.project_data.get('canvas_config', {
            'width': 1920,
            'height': 1080,
            'ratio': 'original'
        })
    
    def get_fps(self) -> float:
        """获取帧率"""
        if not self.project_data:
            self.load_project()
        
        return self.project_data.get('fps', 30.0)


if __name__ == "__main__":
    # 测试代码
    parser = JianyingProjectParser(".")
    parser.load_project()
    effects = parser.extract_text_effects()
    
    print(f"找到 {len(effects)} 个文本特效:")
    for effect in effects:
        print(f"- ID: {effect.id}")
        print(f"  名称: {effect.name}")
        print(f"  文本: {effect.text_content}")
        print(f"  字体: {effect.style.font_path}")
        print(f"  动画数量: {len(effect.animations)}")
        print()

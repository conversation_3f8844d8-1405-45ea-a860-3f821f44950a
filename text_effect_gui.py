#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映文本特效预览生成器 - 用户界面
支持文件选择、文字输入、特效预览等功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from typing import List, Optional
from PIL import Image, ImageTk
import json

from jianying_parser import JianyingProjectParser, TextEffect
from text_renderer import TextRenderer
from apng_generator import create_apng_from_frames


class TextEffectGUI:
    """剪映文本特效预览生成器GUI"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("剪映文本特效预览生成器")
        self.root.geometry("1000x700")
        
        # 数据
        self.project_parser = None
        self.text_effects = []
        self.current_effect = None
        self.preview_frames = []
        self.current_frame_index = 0
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        
        # 启动预览动画定时器
        self.animation_timer = None
        self.is_playing = False
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 左侧面板
        self.left_panel = ttk.Frame(self.main_frame, width=300)
        self.left_panel.pack_propagate(False)
        
        # 项目选择区域
        self.project_frame = ttk.LabelFrame(self.left_panel, text="剪映工程", padding=10)
        
        self.project_path_var = tk.StringVar()
        self.project_entry = ttk.Entry(self.project_frame, textvariable=self.project_path_var, width=30)
        self.browse_btn = ttk.Button(self.project_frame, text="浏览", command=self.browse_project)
        self.load_btn = ttk.Button(self.project_frame, text="加载工程", command=self.load_project)
        
        # 特效选择区域
        self.effect_frame = ttk.LabelFrame(self.left_panel, text="文本特效", padding=10)
        
        self.effect_listbox = tk.Listbox(self.effect_frame, height=8)
        self.effect_listbox.bind('<<ListboxSelect>>', self.on_effect_select)
        
        self.effect_scrollbar = ttk.Scrollbar(self.effect_frame, orient="vertical")
        self.effect_listbox.config(yscrollcommand=self.effect_scrollbar.set)
        self.effect_scrollbar.config(command=self.effect_listbox.yview)
        
        # 文本输入区域
        self.text_frame = ttk.LabelFrame(self.left_panel, text="自定义文本", padding=10)
        
        self.text_var = tk.StringVar(value="示例文本")
        self.text_entry = ttk.Entry(self.text_frame, textvariable=self.text_var, width=30)
        
        # 预览控制区域
        self.control_frame = ttk.LabelFrame(self.left_panel, text="预览控制", padding=10)
        
        self.preview_btn = ttk.Button(self.control_frame, text="生成预览", command=self.generate_preview)
        self.play_btn = ttk.Button(self.control_frame, text="播放", command=self.toggle_play)
        self.export_btn = ttk.Button(self.control_frame, text="导出APNG", command=self.export_apng)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.control_frame, variable=self.progress_var, maximum=100)
        
        # 状态标签
        self.status_var = tk.StringVar(value="请选择剪映工程文件")
        self.status_label = ttk.Label(self.control_frame, textvariable=self.status_var)
        
        # 右侧面板
        self.right_panel = ttk.Frame(self.main_frame)
        
        # 预览区域
        self.preview_frame = ttk.LabelFrame(self.right_panel, text="预览", padding=10)
        
        self.preview_canvas = tk.Canvas(self.preview_frame, width=640, height=360, bg='black')
        self.preview_canvas.bind('<Button-1>', self.on_canvas_click)
        
        # 特效信息区域
        self.info_frame = ttk.LabelFrame(self.right_panel, text="特效信息", padding=10)
        
        self.info_text = scrolledtext.ScrolledText(self.info_frame, width=50, height=10, wrap=tk.WORD)
        self.info_text.config(state=tk.DISABLED)
    
    def setup_layout(self):
        """设置布局"""
        # 主框架
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 项目选择区域
        self.project_frame.pack(fill=tk.X, pady=(0, 10))
        self.project_entry.pack(fill=tk.X, pady=(0, 5))
        self.browse_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.load_btn.pack(side=tk.LEFT)
        
        # 特效选择区域
        self.effect_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.effect_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.effect_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 文本输入区域
        self.text_frame.pack(fill=tk.X, pady=(0, 10))
        self.text_entry.pack(fill=tk.X)
        
        # 预览控制区域
        self.control_frame.pack(fill=tk.X)
        self.preview_btn.pack(fill=tk.X, pady=(0, 5))
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.export_btn.pack(side=tk.LEFT)
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        self.status_label.pack(fill=tk.X, pady=(5, 0))
        
        # 右侧面板
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 预览区域
        self.preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.preview_canvas.pack(expand=True)
        
        # 特效信息区域
        self.info_frame.pack(fill=tk.X)
        self.info_text.pack(fill=tk.BOTH, expand=True)
    
    def browse_project(self):
        """浏览剪映工程文件夹"""
        folder_path = filedialog.askdirectory(title="选择剪映工程文件夹")
        if folder_path:
            self.project_path_var.set(folder_path)
    
    def load_project(self):
        """加载剪映工程"""
        project_path = self.project_path_var.get()
        if not project_path:
            messagebox.showerror("错误", "请选择剪映工程文件夹")
            return
        
        try:
            self.status_var.set("正在加载工程...")
            self.root.update()
            
            # 创建解析器
            self.project_parser = JianyingProjectParser(project_path)
            self.project_parser.load_project()
            
            # 提取文本特效
            self.text_effects = self.project_parser.extract_text_effects()
            
            # 更新特效列表
            self.effect_listbox.delete(0, tk.END)
            for i, effect in enumerate(self.text_effects):
                display_name = effect.name or f"特效 {i+1}"
                if effect.text_content:
                    display_name += f" ({effect.text_content})"
                self.effect_listbox.insert(tk.END, display_name)
            
            self.status_var.set(f"已加载 {len(self.text_effects)} 个文本特效")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载工程失败: {e}")
            self.status_var.set("加载失败")
    
    def on_effect_select(self, event):
        """特效选择事件"""
        selection = self.effect_listbox.curselection()
        if selection:
            index = selection[0]
            self.current_effect = self.text_effects[index]
            self.show_effect_info()
            
            # 如果有原始文本，更新输入框
            if self.current_effect.text_content:
                self.text_var.set(self.current_effect.text_content)
    
    def show_effect_info(self):
        """显示特效信息"""
        if not self.current_effect:
            return
        
        info = f"特效ID: {self.current_effect.id}\n"
        info += f"名称: {self.current_effect.name}\n"
        info += f"原始文本: {self.current_effect.text_content}\n"
        info += f"时长: {self.current_effect.duration/1000000:.1f}秒\n"
        info += f"动画数量: {len(self.current_effect.animations)}\n\n"
        
        # 样式信息
        style = self.current_effect.style
        info += "样式信息:\n"
        info += f"  字体大小: {style.size}\n"
        info += f"  字体路径: {style.font_path}\n"
        info += f"  填充颜色: {style.fill_color}\n"
        info += f"  描边宽度: {style.stroke_width}\n"
        info += f"  阴影数量: {len(style.shadows)}\n"
        
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info)
        self.info_text.config(state=tk.DISABLED)
    
    def generate_preview(self):
        """生成预览"""
        if not self.current_effect:
            messagebox.showerror("错误", "请先选择一个文本特效")
            return
        
        text = self.text_var.get().strip()
        if not text:
            messagebox.showerror("错误", "请输入要预览的文本")
            return
        
        # 在后台线程中生成预览
        threading.Thread(target=self._generate_preview_thread, args=(text,), daemon=True).start()
    
    def _generate_preview_thread(self, text: str):
        """在后台线程中生成预览"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在生成预览..."))
            self.root.after(0, lambda: self.progress_var.set(0))
            
            # 获取画布配置
            canvas_config = self.project_parser.get_canvas_config()
            fps = self.project_parser.get_fps()
            
            # 创建渲染器
            renderer = TextRenderer(canvas_config['width'], canvas_config['height'])
            
            # 渲染帧序列
            frames = renderer.render_text_effect(text, self.current_effect, fps)
            
            # 缩放帧以适应预览画布
            preview_frames = []
            canvas_width = 640
            canvas_height = 360
            
            for i, frame in enumerate(frames):
                # 计算缩放比例
                scale_x = canvas_width / frame.width
                scale_y = canvas_height / frame.height
                scale = min(scale_x, scale_y)
                
                new_width = int(frame.width * scale)
                new_height = int(frame.height * scale)
                
                scaled_frame = frame.resize((new_width, new_height), Image.Resampling.LANCZOS)
                preview_frames.append(scaled_frame)
                
                # 更新进度
                progress = (i + 1) / len(frames) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
            
            self.preview_frames = preview_frames
            self.current_frame_index = 0
            
            # 显示第一帧
            self.root.after(0, self.show_current_frame)
            self.root.after(0, lambda: self.status_var.set(f"预览生成完成，共 {len(frames)} 帧"))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"生成预览失败: {e}"))
            self.root.after(0, lambda: self.status_var.set("生成预览失败"))
    
    def show_current_frame(self):
        """显示当前帧"""
        if not self.preview_frames:
            return
        
        frame = self.preview_frames[self.current_frame_index]
        
        # 转换为PhotoImage
        photo = ImageTk.PhotoImage(frame)
        
        # 清空画布并显示图像
        self.preview_canvas.delete("all")
        
        # 居中显示
        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()
        x = (canvas_width - frame.width) // 2
        y = (canvas_height - frame.height) // 2
        
        self.preview_canvas.create_image(x, y, anchor=tk.NW, image=photo)
        
        # 保持引用防止被垃圾回收
        self.preview_canvas.image = photo
    
    def toggle_play(self):
        """切换播放状态"""
        if not self.preview_frames:
            messagebox.showwarning("警告", "请先生成预览")
            return
        
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            self.play_btn.config(text="暂停")
            self.start_animation()
        else:
            self.play_btn.config(text="播放")
            self.stop_animation()
    
    def start_animation(self):
        """开始动画"""
        if self.is_playing and self.preview_frames:
            self.show_current_frame()
            self.current_frame_index = (self.current_frame_index + 1) % len(self.preview_frames)
            self.animation_timer = self.root.after(33, self.start_animation)  # 约30fps
    
    def stop_animation(self):
        """停止动画"""
        if self.animation_timer:
            self.root.after_cancel(self.animation_timer)
            self.animation_timer = None
    
    def on_canvas_click(self, event):
        """画布点击事件"""
        if self.preview_frames and not self.is_playing:
            # 手动切换帧
            self.current_frame_index = (self.current_frame_index + 1) % len(self.preview_frames)
            self.show_current_frame()
    
    def export_apng(self):
        """导出APNG"""
        if not self.preview_frames:
            messagebox.showwarning("警告", "请先生成预览")
            return
        
        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存APNG文件",
            defaultextension=".apng",
            filetypes=[("APNG文件", "*.apng"), ("PNG文件", "*.png"), ("所有文件", "*.*")]
        )
        
        if file_path:
            threading.Thread(target=self._export_apng_thread, args=(file_path,), daemon=True).start()
    
    def _export_apng_thread(self, file_path: str):
        """在后台线程中导出APNG"""
        try:
            self.root.after(0, lambda: self.status_var.set("正在导出APNG..."))
            
            # 使用原始分辨率的帧
            canvas_config = self.project_parser.get_canvas_config()
            fps = self.project_parser.get_fps()
            
            renderer = TextRenderer(canvas_config['width'], canvas_config['height'])
            text = self.text_var.get().strip()
            frames = renderer.render_text_effect(text, self.current_effect, fps)
            
            # 创建APNG
            success = create_apng_from_frames(frames, file_path, fps)
            
            if success:
                self.root.after(0, lambda: self.status_var.set(f"APNG已导出: {file_path}"))
                self.root.after(0, lambda: messagebox.showinfo("成功", f"APNG文件已保存到:\n{file_path}"))
            else:
                self.root.after(0, lambda: self.status_var.set("导出失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", "导出APNG失败"))
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"导出失败: {e}"))
            self.root.after(0, lambda: self.status_var.set("导出失败"))
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    app = TextEffectGUI()
    app.run()

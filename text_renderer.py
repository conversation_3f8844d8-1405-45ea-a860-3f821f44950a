#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本特效渲染引擎
支持字体、颜色、阴影、描边、动画等特效的渲染
"""

import os
import math
from typing import List, Tuple, Dict
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from jianying_parser import TextStyle, TextEffect, TextAnimation


class TextRenderer:
    """文本特效渲染器"""
    
    def __init__(self, canvas_width: int = 1920, canvas_height: int = 1080):
        """
        初始化渲染器
        
        Args:
            canvas_width: 画布宽度
            canvas_height: 画布高度
        """
        self.canvas_width = canvas_width
        self.canvas_height = canvas_height
        self.default_font_size = 48
        
    def load_font(self, font_path: str, size: float) -> ImageFont.FreeTypeFont:
        """加载字体"""
        try:
            if font_path and os.path.exists(font_path):
                return ImageFont.truetype(font_path, int(size * 3))  # 放大3倍用于高质量渲染
            else:
                # 使用系统默认字体
                try:
                    # macOS 系统字体
                    return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", int(size * 3))
                except:
                    try:
                        # Windows 系统字体
                        return ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", int(size * 3))
                    except:
                        # 使用PIL默认字体
                        return ImageFont.load_default()
        except Exception as e:
            print(f"加载字体失败: {e}")
            return ImageFont.load_default()
    
    def color_to_rgba(self, color: List[float]) -> Tuple[int, int, int, int]:
        """将浮点颜色转换为RGBA整数"""
        if len(color) == 3:
            color = color + [1.0]
        return tuple(int(c * 255) for c in color)
    
    def create_text_mask(self, text: str, font: ImageFont.FreeTypeFont) -> Image.Image:
        """创建文本遮罩"""
        # 获取文本尺寸
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 创建足够大的画布
        mask_width = text_width + 100
        mask_height = text_height + 100
        
        mask = Image.new('L', (mask_width, mask_height), 0)
        draw = ImageDraw.Draw(mask)
        
        # 绘制文本
        x = 50 - bbox[0]
        y = 50 - bbox[1]
        draw.text((x, y), text, font=font, fill=255)
        
        return mask
    
    def apply_stroke(self, image: Image.Image, mask: Image.Image,
                    stroke_color: Tuple[int, int, int, int], stroke_width: float) -> Image.Image:
        """应用描边效果"""
        if stroke_width <= 0:
            return image

        try:
            # 使用简单的膨胀方法创建描边效果
            stroke_width_px = max(1, int(stroke_width * 10))  # 转换为像素

            # 创建描边图层
            stroke_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))

            # 在多个方向绘制文本来模拟描边
            for dx in range(-stroke_width_px, stroke_width_px + 1):
                for dy in range(-stroke_width_px, stroke_width_px + 1):
                    if dx == 0 and dy == 0:
                        continue
                    if dx*dx + dy*dy <= stroke_width_px*stroke_width_px:
                        # 创建偏移的遮罩
                        offset_mask = Image.new('L', image.size, 0)
                        if 0 <= dx < image.width and 0 <= dy < image.height:
                            offset_mask.paste(mask, (dx, dy))
                        stroke_layer.paste(stroke_color, mask=offset_mask)

            # 合成描边和原图
            result = Image.alpha_composite(stroke_layer, image)
            return result
        except Exception as e:
            print(f"应用描边失败: {e}")
            return image
    
    def apply_shadow(self, image: Image.Image, mask: Image.Image, 
                    shadow_info: Dict, offset_x: int = 0, offset_y: int = 0) -> Image.Image:
        """应用阴影效果"""
        try:
            distance = shadow_info.get('distance', 0)
            angle = shadow_info.get('angle', -90)
            feather = shadow_info.get('feather', 0)
            
            # 计算阴影偏移
            angle_rad = math.radians(angle)
            shadow_x = int(distance * math.cos(angle_rad)) + offset_x
            shadow_y = int(distance * math.sin(angle_rad)) + offset_y
            
            # 获取阴影颜色
            shadow_color = [0, 0, 0, 0.5]  # 默认半透明黑色
            content = shadow_info.get('content', {})
            if content and 'solid' in content:
                shadow_color = content['solid'].get('color', shadow_color)
            
            shadow_rgba = self.color_to_rgba(shadow_color)
            
            # 创建阴影图层
            shadow_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
            
            # 创建偏移的遮罩
            shadow_mask = Image.new('L', image.size, 0)
            mask_x = max(0, shadow_x)
            mask_y = max(0, shadow_y)
            
            if mask_x < image.width and mask_y < image.height:
                # 计算粘贴区域
                paste_width = min(mask.width, image.width - mask_x)
                paste_height = min(mask.height, image.height - mask_y)
                
                if paste_width > 0 and paste_height > 0:
                    crop_mask = mask.crop((0, 0, paste_width, paste_height))
                    shadow_mask.paste(crop_mask, (mask_x, mask_y))
            
            # 应用羽化
            if feather > 0:
                shadow_mask = shadow_mask.filter(ImageFilter.GaussianBlur(feather * 3))
            
            # 填充阴影颜色
            shadow_layer.paste(shadow_rgba, mask=shadow_mask)
            
            # 合成阴影和原图
            result = Image.alpha_composite(shadow_layer, image)
            return result
            
        except Exception as e:
            print(f"应用阴影失败: {e}")
            return image
    
    def render_text_frame(self, text: str, style: TextStyle, 
                         frame_time: float = 0.0, total_duration: float = 3.0) -> Image.Image:
        """
        渲染单帧文本
        
        Args:
            text: 要渲染的文本
            style: 文本样式
            frame_time: 当前帧时间（秒）
            total_duration: 总时长（秒）
        
        Returns:
            渲染后的图像
        """
        # 创建画布
        canvas = Image.new('RGBA', (self.canvas_width, self.canvas_height), (0, 0, 0, 0))
        
        if not text.strip():
            return canvas
        
        # 加载字体
        font = self.load_font(style.font_path, style.size)
        
        # 创建文本遮罩
        text_mask = self.create_text_mask(text, font)
        
        # 获取文本尺寸用于居中
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 计算居中位置
        center_x = (self.canvas_width - text_width) // 2
        center_y = (self.canvas_height - text_height) // 2
        
        # 创建文本图层
        text_layer = Image.new('RGBA', (self.canvas_width, self.canvas_height), (0, 0, 0, 0))
        
        # 填充文本颜色
        fill_color = self.color_to_rgba(style.fill_color)
        text_layer.paste(fill_color, mask=text_mask)
        
        # 将文本图层放置到画布中心
        canvas.paste(text_layer, (center_x - 50, center_y - 50), text_layer)
        
        # 应用描边
        if style.stroke_width > 0 and style.stroke_color:
            stroke_color = self.color_to_rgba(style.stroke_color)
            canvas = self.apply_stroke(canvas, text_mask, stroke_color, style.stroke_width)
        
        # 应用阴影
        for shadow in style.shadows:
            canvas = self.apply_shadow(canvas, text_mask, shadow, center_x - 50, center_y - 50)
        
        # 应用内阴影
        for inner_shadow in style.inner_shadows:
            canvas = self.apply_shadow(canvas, text_mask, inner_shadow, center_x - 50, center_y - 50)
        
        return canvas
    
    def apply_animation_transform(self, image: Image.Image, animation: TextAnimation, 
                                frame_time: float, total_duration: float) -> Image.Image:
        """应用动画变换"""
        try:
            anim_duration = animation.duration / 1000000.0  # 转换为秒
            anim_start = animation.start_time / 1000000.0
            
            # 计算动画进度
            if frame_time < anim_start:
                progress = 0.0
            elif frame_time > anim_start + anim_duration:
                progress = 1.0
            else:
                progress = (frame_time - anim_start) / anim_duration
            
            # 根据动画类型应用变换
            if animation.animation_type == "in":
                # 淡入动画
                alpha = int(255 * progress)
                if alpha < 255:
                    # 创建带透明度的图像
                    result = Image.new('RGBA', image.size, (0, 0, 0, 0))
                    temp = image.copy()
                    temp.putalpha(alpha)
                    result.paste(temp, (0, 0), temp)
                    return result
            elif animation.animation_type == "out":
                # 淡出动画
                alpha = int(255 * (1.0 - progress))
                if alpha < 255:
                    result = Image.new('RGBA', image.size, (0, 0, 0, 0))
                    temp = image.copy()
                    temp.putalpha(alpha)
                    result.paste(temp, (0, 0), temp)
                    return result
            elif animation.animation_type == "loop":
                # 循环动画（简单的缩放效果）
                scale = 1.0 + 0.1 * math.sin(progress * 2 * math.pi)
                if scale != 1.0:
                    new_width = int(image.width * scale)
                    new_height = int(image.height * scale)
                    scaled = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    
                    # 居中放置
                    result = Image.new('RGBA', image.size, (0, 0, 0, 0))
                    x = (image.width - new_width) // 2
                    y = (image.height - new_height) // 2
                    result.paste(scaled, (x, y), scaled)
                    return result
            
            return image
            
        except Exception as e:
            print(f"应用动画变换失败: {e}")
            return image
    
    def render_text_effect(self, text: str, effect: TextEffect, fps: float = 30.0) -> List[Image.Image]:
        """
        渲染完整的文本特效动画
        
        Args:
            text: 要渲染的文本
            effect: 文本特效配置
            fps: 帧率
        
        Returns:
            帧序列列表
        """
        duration_seconds = effect.duration / 1000000.0  # 转换为秒
        total_frames = int(duration_seconds * fps)
        frames = []
        
        for frame_idx in range(total_frames):
            frame_time = frame_idx / fps
            
            # 渲染基础文本帧
            frame = self.render_text_frame(text, effect.style, frame_time, duration_seconds)
            
            # 应用动画效果
            for animation in effect.animations:
                frame = self.apply_animation_transform(frame, animation, frame_time, duration_seconds)
            
            frames.append(frame)
        
        return frames


if __name__ == "__main__":
    # 测试代码
    from jianying_parser import TextStyle, TextEffect
    
    # 创建测试样式
    style = TextStyle()
    style.size = 60
    style.fill_color = [1.0, 0.8, 0.3, 1.0]  # 金黄色
    style.stroke_color = [0.6, 0.1, 0.0, 1.0]  # 深红色描边
    style.stroke_width = 2.0
    
    # 创建测试特效
    effect = TextEffect()
    effect.style = style
    effect.duration = 3000000  # 3秒
    
    # 渲染测试
    renderer = TextRenderer()
    frames = renderer.render_text_effect("测试文本", effect)
    
    print(f"渲染完成，共 {len(frames)} 帧")
    
    # 保存第一帧作为测试
    if frames:
        frames[0].save("test_frame.png")
        print("测试帧已保存为 test_frame.png")

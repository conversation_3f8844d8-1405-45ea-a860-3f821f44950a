#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剪映文本特效预览生成器 - 主程序
"""

import sys
import os
import traceback
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from text_effect_gui import TextEffectGUI
except ImportError as e:
    print(f"导入GUI模块失败: {e}")
    print("请确保所有依赖库已安装:")
    print("pip install pillow")
    sys.exit(1)


def check_dependencies():
    """检查依赖库"""
    required_modules = [
        ('PIL', 'Pillow'),
        ('tkinter', 'tkinter (通常随Python安装)'),
    ]
    
    missing_modules = []
    
    for module_name, install_name in required_modules:
        try:
            __import__(module_name)
        except ImportError:
            missing_modules.append(install_name)
    
    if missing_modules:
        print("缺少以下依赖库:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请使用以下命令安装:")
        print("pip install pillow")
        return False
    
    return True


def main():
    """主函数"""
    print("剪映文本特效预览生成器")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 创建并运行GUI
        app = TextEffectGUI()
        app.run()
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()

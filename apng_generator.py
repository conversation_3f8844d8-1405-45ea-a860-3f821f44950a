#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APNG动图生成器
将渲染的文本特效帧序列合成为APNG动态图片
"""

import os
import struct
import zlib
from typing import List, Optional
from PIL import Image
import io


class APNGGenerator:
    """APNG动图生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.frames = []
        self.delays = []
        self.default_delay = 33  # 默认33ms (约30fps)
    
    def add_frame(self, image: Image.Image, delay: int = None):
        """
        添加一帧到动画序列
        
        Args:
            image: PIL图像对象
            delay: 帧延迟时间（毫秒），None使用默认值
        """
        # 确保图像是RGBA模式
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        self.frames.append(image)
        self.delays.append(delay if delay is not None else self.default_delay)
    
    def add_frames(self, images: List[Image.Image], fps: float = 30.0):
        """
        批量添加帧
        
        Args:
            images: 图像列表
            fps: 帧率
        """
        delay = int(1000 / fps)  # 转换为毫秒
        for image in images:
            self.add_frame(image, delay)
    
    def _write_png_chunk(self, chunk_type: bytes, data: bytes) -> bytes:
        """写入PNG块"""
        length = len(data)
        crc = zlib.crc32(chunk_type + data) & 0xffffffff
        return struct.pack('>I', length) + chunk_type + data + struct.pack('>I', crc)
    
    def _create_ihdr_chunk(self, width: int, height: int) -> bytes:
        """创建IHDR块"""
        data = struct.pack('>IIBBBBB', width, height, 8, 6, 0, 0, 0)
        return self._write_png_chunk(b'IHDR', data)
    
    def _create_actl_chunk(self, num_frames: int, num_plays: int = 0) -> bytes:
        """创建acTL块（动画控制）"""
        data = struct.pack('>II', num_frames, num_plays)
        return self._write_png_chunk(b'acTL', data)
    
    def _create_fctl_chunk(self, sequence_number: int, width: int, height: int, 
                          x_offset: int, y_offset: int, delay_num: int, delay_den: int,
                          dispose_op: int = 0, blend_op: int = 0) -> bytes:
        """创建fcTL块（帧控制）"""
        data = struct.pack('>IIIIIHHBB', sequence_number, width, height, 
                          x_offset, y_offset, delay_num, delay_den, dispose_op, blend_op)
        return self._write_png_chunk(b'fcTL', data)
    
    def _create_fdat_chunk(self, sequence_number: int, frame_data: bytes) -> bytes:
        """创建fdAT块（帧数据）"""
        data = struct.pack('>I', sequence_number) + frame_data
        return self._write_png_chunk(b'fdAT', data)
    
    def _get_png_data(self, image: Image.Image) -> bytes:
        """获取PNG图像数据"""
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        png_data = buffer.getvalue()
        
        # 提取IDAT数据
        pos = 8  # 跳过PNG签名
        idat_data = b''
        
        while pos < len(png_data):
            length = struct.unpack('>I', png_data[pos:pos+4])[0]
            chunk_type = png_data[pos+4:pos+8]
            chunk_data = png_data[pos+8:pos+8+length]
            
            if chunk_type == b'IDAT':
                idat_data += chunk_data
            
            pos += 12 + length  # 4(length) + 4(type) + length + 4(crc)
        
        return idat_data
    
    def save_apng(self, output_path: str, optimize: bool = True) -> bool:
        """
        保存为APNG文件
        
        Args:
            output_path: 输出文件路径
            optimize: 是否优化文件大小
        
        Returns:
            是否保存成功
        """
        if not self.frames:
            print("没有帧数据可保存")
            return False
        
        try:
            # 获取第一帧的尺寸
            first_frame = self.frames[0]
            width, height = first_frame.size
            
            # 创建输出文件
            with open(output_path, 'wb') as f:
                # 写入PNG签名
                f.write(b'\x89PNG\r\n\x1a\n')
                
                # 写入IHDR块
                f.write(self._create_ihdr_chunk(width, height))
                
                # 写入acTL块
                f.write(self._create_actl_chunk(len(self.frames)))
                
                sequence_number = 0
                
                # 处理第一帧（作为默认图像）
                first_frame_data = self._get_png_data(first_frame)
                
                # 写入第一帧的fcTL块
                delay_ms = self.delays[0]
                f.write(self._create_fctl_chunk(sequence_number, width, height, 0, 0, 
                                              delay_ms, 1000))
                sequence_number += 1
                
                # 写入第一帧的IDAT块
                f.write(self._write_png_chunk(b'IDAT', first_frame_data))
                
                # 处理后续帧
                for i in range(1, len(self.frames)):
                    frame = self.frames[i]
                    delay_ms = self.delays[i]
                    
                    # 确保帧尺寸一致
                    if frame.size != (width, height):
                        frame = frame.resize((width, height), Image.Resampling.LANCZOS)
                    
                    # 写入fcTL块
                    f.write(self._create_fctl_chunk(sequence_number, width, height, 0, 0,
                                                  delay_ms, 1000))
                    sequence_number += 1
                    
                    # 获取帧数据并写入fdAT块
                    frame_data = self._get_png_data(frame)
                    f.write(self._create_fdat_chunk(sequence_number, frame_data))
                    sequence_number += 1
                
                # 写入IEND块
                f.write(self._write_png_chunk(b'IEND', b''))
            
            print(f"APNG文件已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"保存APNG文件失败: {e}")
            return False
    
    def save_gif(self, output_path: str, optimize: bool = True) -> bool:
        """
        保存为GIF文件（备用方案）
        
        Args:
            output_path: 输出文件路径
            optimize: 是否优化文件大小
        
        Returns:
            是否保存成功
        """
        if not self.frames:
            print("没有帧数据可保存")
            return False
        
        try:
            # 转换为RGB模式（GIF不支持透明度）
            rgb_frames = []
            for frame in self.frames:
                # 创建白色背景
                background = Image.new('RGB', frame.size, (255, 255, 255))
                if frame.mode == 'RGBA':
                    background.paste(frame, mask=frame.split()[-1])  # 使用alpha通道作为遮罩
                else:
                    background.paste(frame)
                rgb_frames.append(background)
            
            # 保存GIF
            rgb_frames[0].save(
                output_path,
                save_all=True,
                append_images=rgb_frames[1:],
                duration=self.delays,
                loop=0,
                optimize=optimize
            )
            
            print(f"GIF文件已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"保存GIF文件失败: {e}")
            return False
    
    def clear(self):
        """清空所有帧数据"""
        self.frames.clear()
        self.delays.clear()
    
    def get_frame_count(self) -> int:
        """获取帧数"""
        return len(self.frames)
    
    def get_total_duration(self) -> int:
        """获取总时长（毫秒）"""
        return sum(self.delays)


def create_apng_from_frames(frames: List[Image.Image], output_path: str, 
                           fps: float = 30.0, optimize: bool = True) -> bool:
    """
    从帧列表创建APNG文件的便捷函数
    
    Args:
        frames: 图像帧列表
        output_path: 输出文件路径
        fps: 帧率
        optimize: 是否优化
    
    Returns:
        是否创建成功
    """
    generator = APNGGenerator()
    generator.add_frames(frames, fps)
    
    # 尝试保存为APNG，失败则保存为GIF
    if not generator.save_apng(output_path, optimize):
        gif_path = output_path.replace('.apng', '.gif').replace('.png', '.gif')
        return generator.save_gif(gif_path, optimize)
    
    return True


if __name__ == "__main__":
    # 测试代码
    from PIL import Image, ImageDraw
    
    # 创建测试帧
    frames = []
    for i in range(30):  # 30帧，1秒动画
        img = Image.new('RGBA', (400, 200), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制一个移动的圆
        x = 50 + i * 10
        y = 100
        draw.ellipse([x-20, y-20, x+20, y+20], fill=(255, 100, 100, 255))
        
        frames.append(img)
    
    # 创建APNG
    success = create_apng_from_frames(frames, "test_animation.apng", fps=30)
    if success:
        print("测试动画创建成功")
    else:
        print("测试动画创建失败")
